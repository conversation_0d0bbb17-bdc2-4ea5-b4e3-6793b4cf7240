# Cargar factory_bot_rails
begin
  require 'factory_bot_rails'
  include ActionDispatch::TestProcess
rescue LoadError
  puts "AVISO: factory_bot_rails no está disponible. Algunas funcionalidades de seeds pueden no estar disponibles."
end

# Deshabilitamos Sunspot para evitar errores durante la creación de seeds
Sunspot.session = Sunspot::Rails::StubSessionProxy.new(Sunspot.session)

# ==================== ROLES ====================
puts "Creando roles básicos..."
Pioneer::Role.where(id: 1).first_or_initialize.tap do |record|
  record.name = 'administrator'
  record.created_at = Time.parse('2025-02-23 17:34:04 -0300')
  record.updated_at = Time.parse('2025-02-23 17:34:04 -0300')
  record.save!(validate: false)
end

Pioneer::Role.where(id: 2).first_or_initialize.tap do |record|
  record.name = 'manager'
  record.created_at = Time.parse('2025-02-23 17:34:04 -0300')
  record.updated_at = Time.parse('2025-02-23 17:34:04 -0300')
  record.save!(validate: false)
end

Pioneer::Role.where(id: 3).first_or_initialize.tap do |record|
  record.name = 'user'
  record.created_at = Time.parse('2025-02-23 17:34:04 -0300')
  record.updated_at = Time.parse('2025-02-23 17:34:04 -0300')
  record.save!(validate: false)
end

# ==================== ADMIN ====================
puts "Creando usuario administrador..."
Pioneer::Admin.where(id: 1).first_or_initialize.tap do |record|
  record.first_name = 'Admin'
  record.last_name = 'User'
  record.crypted_password = '400$8$1c$b30ad891edb731ae$4fb37156ee49b1acc2c9e5f4c972a64fa05729fc54418991614acd37741afe3a'
  record.password_salt = 'nARZSxegyZrapubrCizS'
  record.password = 'Avenidamas2019'
  record.password_confirmation = 'Avenidamas2019'
  record.email = '<EMAIL>'
  record.persistence_token = 'afe1c1be80f17900defa54d999f3f5435033c44b'
  record.single_access_token = '89444cc563d4eb5f21ce713d2e29ea1e66393990'
  record.perishable_token = 'dbbf4ccaeb9a98131f7dabb02b5aa7db16c3d96f'
  record.login_count = 910
  record.failed_login_count = 0
  record.last_request_at = '2025-02-11 20:44:01'
  record.current_login_at = '2025-02-11 20:13:01'
  record.last_login_at = '2025-02-11 20:03:07'
  record.current_login_ip = '**************'
  record.last_login_ip = '*************'
  record.network = 'AR'
  record.created_at = '2017-06-29 00:22:30'
  record.updated_at = '2025-02-11 20:44:01'
  record.galilei = 1
  record.be_notified = 0
  record.role_id = 1
  record.save!(validate: false)
end

# ==================== USER ====================
puts "Creando usuario regular..."
User.where(id: 1).first_or_initialize.tap do |record|
  record.login = 'eshop'
  record.email = '<EMAIL>'
  record.password = 'shopadmin_2020!'
  record.password_confirmation = 'shopadmin_2020!'
  record.crypted_password = '400$8$2d$b90e38d6ecf459bf$a94936f3f6140a5c0c216773b2b2ec620b5330ea286fba2efedfe35743f820c5'
  record.password_salt = '9a2nhy0Omv9c2aGeGaZL'
  record.roles_mask = '3'
  record.persistence_token = '59e5a853d1c5f08e9f58f65ae109e9e6495072a9cf13c8eb0f242c32015443e2b15ad57a2216b5c9d8b3eedc3e38c258c8c6dae2175cf5de4cb2aa53a75f2b57'
  record.login_count = '9272'
  record.last_request_at = '2025-01-14 17:13:46'
  record.last_login_at = '2024-08-08 18:58:22'
  record.current_login_at = '2024-08-16 15:15:48'
  record.last_login_ip = '**************'
  record.current_login_ip = '**************'
  record.perishable_token = 'LtNvd9vQkUZnbF-sd3Ob'
  record.network = 'AR'
  record.created_at = '2016-12-12 04:10:18'
  record.updated_at = '2025-01-14 17:13:46'
  record.type = 'SocialUser'
  record.braintree_customer_id = nil
  record.braintree_data = nil
  record.followers_count = '0'
  record.mercadopago_customer_id = nil
  record.mercadopago_data = nil
  record.save!(validate: false)
end
