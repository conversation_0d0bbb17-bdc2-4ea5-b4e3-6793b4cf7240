# Este archivo es necesario para manejar tipos de datos específicos en MySQL

require 'active_record/connection_adapters/mysql2_adapter'

class ActiveRecord::ConnectionAdapters::Mysql2Adapter
  # Define el tipo primary_key estándar
  NATIVE_DATABASE_TYPES[:primary_key] = "int(11) auto_increment PRIMARY KEY"

  # Define el tipo bigserial para compatibilidad con PostgreSQL
  NATIVE_DATABASE_TYPES[:bigserial] = "bigint(20) auto_increment PRIMARY KEY"
 end
