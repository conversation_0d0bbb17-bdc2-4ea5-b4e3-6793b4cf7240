RocketChatNotifier.configure do |config|
	# replace webhook_url with your incoming rocket chat webhook url
	config.webhook_url_platform = "https://chat.avenida.com.ar/hooks/#{ROCKETCHAT_PLATFORM_TOKEN}"
	config.webhook_url_pav = "https://chat.avenida.com.ar/hooks/#{ROCKETCHAT_PRODUCTS_ACTIVES_VARIANTS}"
	config.webhook_url_payments = "https://chat.avenida.com.ar/hooks/#{ROCKETCHAT_PAYMENTS_TOKEN}"
	config.webhook_url_matrix = ROCKETCHAT_MATRIX_TOKEN.present? &&
		"https://chat.avenida.com.ar/hooks/#{ROCKETCHAT_MATRIX_TOKEN}"
	# config.webhook_url_blister = "https://chat.avenida.com.ar/hooks/#{ROCKETCHAT_BLISTER_TOKEN}"

	# enable verbose debug information with the following line | default: false
	# config.verbose_mode = true
end
