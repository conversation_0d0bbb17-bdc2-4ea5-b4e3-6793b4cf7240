module Pioneer
  module Mimoto
    class CustomerReservationPurchasesController < Pioneer::ApplicationController
      before_filter :set_filter_values, only: [:index]
      before_filter :find_reservation, only: [:destroy]
      before_filter :check_ns_role
      before_filter :check_owner_mimoto

      layout 'bootstrap_layout'

      BNA_MIMOTO_ID = 43

      def index
        # @stores = current_user.has_role?(:administrator) ? Mkp::Store.all : [current_user.role.store]
        # se fuerza a reservas de mimoto
        if Mkp::Store.exists?(BNA_MIMOTO_ID)
          @stores = [Mkp::Store.find(BNA_MIMOTO_ID)]
          @reservations = CustomerReservationPurchases.where(store_id: @stores.map(&:id)).includes(:product).includes(:store)
          @reservations = @reservations.order(created_at: :desc).paginate(page: params[:page])
          @reservations = @reservations.search_query(@search_query) if @search_query.present?
        else
          # Si no existe la tienda, mostrar una lista vacía
          @stores = []
          @reservations = CustomerReservationPurchases.none.paginate(page: 1, per_page: 10)
          flash.now[:alert] = "La tienda Mimoto (ID: #{BNA_MIMOTO_ID}) no existe en la base de datos."
        end
      end


      def destroy
       @reservation.destroy
       redirect_to mimoto_customer_reservation_purchases_url
      end

      private

      def find_reservation
        @reservation = CustomerReservationPurchases.find(params[:id])
      end

      def set_filter_values # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/MethodLength, Metrics/PerceivedComplexity
        @search_query = params[:query] if params[:query].present?
      end

      def check_ns_role
        redirect_to home_url, alert: 'Have not permissions' unless current_user.nacion_servicios_admin? or current_user.has_role?(:administrator)
      end

      def check_owner_mimoto
        # Verificar si existe la tienda Mimoto
        if Mkp::Store.exists?(BNA_MIMOTO_ID)
          # Si existe, verificar si el usuario es dueño
          redirect_to home_url, alert: 'Have not permissions' unless current_user.is_store_owner?(Mkp::Store.find(BNA_MIMOTO_ID))
        else
          # Si no existe la tienda, redirigir al home
          redirect_to home_url, alert: "La tienda Mimoto (ID: #{BNA_MIMOTO_ID}) no existe en la base de datos."
        end
      end
    end
  end
end
