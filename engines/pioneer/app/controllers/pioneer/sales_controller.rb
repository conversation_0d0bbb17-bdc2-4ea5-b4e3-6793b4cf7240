require 'sidekiq/api'

module Pioneer
  class SalesController < Pioneer::ApplicationController # rubocop:disable Metrics/ClassLength
    include Reporting::Streamable

    before_filter :have_permissions_to_read
    before_filter :have_permissions_to_write
    before_filter :set_filter_values, only: %i[index export]
    before_filter :find_orders, only: %i[index export]
    before_filter :find_order, only: %i[show cancel]

    layout 'bootstrap_layout'

    def index
      respond_to do |format|
        format.html do
          @orders = @orders.page(params[:page])
        end

        format.json { @orders = @orders.page(params[:page]) }
        format.csv { render_csv(OrderExporter, network: @network, orders: @orders.limit(1000)) }
      end
    end

    def show
      render layout: false
    end

    def cancel
      service = "Gateways::Refunds::#{@order.gateway.camelize}".constantize.new(@order)
      service.perform

      render layout: false, json: { status: 200, message: service.status, success: service.valid }
    end

    def refund
      service = "Gateways::Refunds::#{@order.gateway.camelize}".constantize.new(@order)
      service.perform

      render layout: false, json: { status: 200, message: service.status, success: service.valid }
    end

    def eval_between_dates(params)
      if params[:created_at_gte].present? && params[:created_at_lt].present?
        return { from: params[:created_at_gte],  to: params[:created_at_lt] }
      end

      nil
    end

    def export # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
      schedule_job = Sidekiq::ScheduledSet.new.size
      time_to_run = schedule_job.positive? ? schedule_job * 4 : 4
      if @orders.any?
        export = current_user.role.store.exports.orders.create
        dates = eval_between_dates(params)
        ExportSalesWorker.perform_in(time_to_run.minutes, @orders.map(&:id), export.id, dates)
        flash[:success] = "Tu export estará disponible a
                           las: #{(Time.zone.now + time_to_run.minutes).strftime('%H:%M')}
                           aproximadamente, el link para descargar el
                           export aparecerá en el listado de exports"
      else
        flash[:error] = 'La búsqueda actual no contiene órdenes,
                         por favor intentá de nuevo'
      end
      redirect_to sales_path
    end

    def tracking
      label = Mkp::ShipmentLabel.find_by id: params[:label_id]
      if label
        render json: build_tracking(label), status: 200
      else
        render json: { error: 'Label not found' }, status: 404
      end
    end

    private

    def created_date_current(date = nil, gte = nil)
      return date.present? ? Time.zone.parse(date).to_datetime.beginning_of_day : nil if gte

      date.present? ? Time.zone.parse(date).to_datetime.end_of_day : nil
    end

    def find_orders # rubocop:disable Metrics/AbcSize, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity, Metrics/MethodLength
      @stores = current_user.has_role?(:administrator) ? Mkp::Store.all : [current_user.role.store]
      created_at_gte = created_date_current(params[:created_at_gte], true)
      created_at_lt = created_date_current(params[:created_at_lt])

      begin
        # Intentar obtener los registros con manejo de errores
        @orders = SaleItem.none

        # Verificar si la tabla tiene la columna store_id
        if SaleItem.column_names.include?('store_id')
          @orders = SaleItem.where(store_id: @stores.map(&:id))
          @orders = @orders.created_at_gte(created_at_gte) if created_at_gte.present?
          @orders = @orders.created_at_lt(created_at_lt) if created_at_lt.present?
          if params[:payment_created_at_gte].present?
            @orders = @orders.with_payment_created_at_gte(params[:payment_created_at_gte])
          end
          if params[:payment_created_at_lt].present?
            @orders = @orders.with_payment_created_at_lt(params[:payment_created_at_lt])
          end
          if params[:with_payment_status].present?
            @orders = @orders.with_payment_status(params[:with_payment_status])
          end
          if params[:with_shipment_status].present?
            @orders = @orders.with_shipment_status(params[:with_shipment_status])
          end
          @orders = @orders.with_store_id(params[:with_store_id]) if params[:with_store_id].present?
          @orders = @orders.search_query(params[:search_query]) if params[:search_query].present?
          @orders = @orders.order('created_at desc')
        end
      rescue => e
        # Si hay un error, devolver una colección vacía
        @orders = SaleItem.none
        Rails.logger.error("Error al buscar ventas: #{e.message}")
      end
    end

    def find_order
      listable_type = params[:type].casecmp('purchase').zero? ? 'Purchase' : 'Mkp::Order'
      @order = SaleItem.find_by(listable_id: params[:id], listable_type: listable_type)
    end

    def have_permissions_to_read # rubocop:disable Naming/PredicateName
      redirect_to home_url, alert: 'Have not permissions' if cannot?(:read, 'Order')
    end

    def have_permissions_to_write # rubocop:disable Naming/PredicateName
      redirect_to orders_url, alert: 'Have not permissions' if cannot?(:crud, 'Order')
    end

    def parser_dates(date)
      Time.zone.parse(date).strftime('%Y-%m-%d')
    end

    def set_filter_values # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/MethodLength, Metrics/PerceivedComplexity
      @created_at_gte = parser_dates(params[:created_at_gte]) if params[:created_at_gte].present?
      @created_at_lt = parser_dates(params[:created_at_lt]) if params[:created_at_lt].present?
      if params[:payment_created_at_gte].present?
        @payment_created_at_gte = parser_dates(params[:payment_created_at_gte])
      end
      if params[:payment_created_at_lt].present?
        @payment_created_at_lt = parser_dates(params[:payment_created_at_lt])
      end
      @with_payment_status = params[:with_payment_status] if params[:with_payment_status].present?
      if params[:with_shipment_status].present?
        @with_shipment_status = params[:with_shipment_status]
      end
      @with_store_id = params[:with_store_id] if params[:with_store_id].present?
      @search_query = params[:search_query] if params[:search_query].present?
    end

    def build_tracking(label) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      result = {
        courier: label.courier.capitalize,
        icon: label.icon,
        status: label.shipment.status,
        tracking: label.tracking_number,
        estimated_delivery_date: estimated_delivery_date(label),
        events: []
      }
      label.shipment.status_changes.order(:created_at).each do |status_change|
        result[:events] << {
          description: I18n.t("pioneer.orders.#{status_change.status}"),
          date: status_change.created_at.strftime('%d-%m-%Y %H:%M')
        }
      end

      result
    end

    def estimated_delivery_date(label)
      return nil if label.estimated_delivery_date.blank?

      label.estimated_delivery_date.strftime('%d-%m-%Y %H:%M')
    end
  end
end
