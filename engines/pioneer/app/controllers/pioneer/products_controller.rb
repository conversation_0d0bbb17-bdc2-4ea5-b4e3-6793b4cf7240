module Pioneer
  class ProductsController < Pioneer::ApplicationController
    before_filter :set_params
    before_filter :check_visa_puntos, only: :index
    before_filter :have_permissions_to_read, only: [:index, :show, :export]
    before_filter :have_permissions_to_write, only: [:export, :show]

    layout 'bootstrap_layout'

    def index
      set_store
      @stores = current_user.role.is_a?("administrator") ? Mkp::Store.all.order(name: :asc) : [@store]
      @shops = @store.present? ? @store.shops.order('title') : Mkp::Shop.all.order('title')
      @shop = @store.shops.find(params[:shop_id]) if params[:shop_id].present? && @store.present?
      set_products
    rescue ActiveRecord::RecordNotFound => e
      redirect_to products_url, error: "Búsqueda no válida, por favor intenta de nuevo"
    end

    def export
      set_store
      @stores = current_user.role.is_a?("administrator") ? Mkp::Store.all.order(name: :asc) : [@store]
      @shop = @store.shops.find(params[:shop_id]) if params[:shop_id].present? && @store.present?
      set_products_for_export

      if @products.any?
        export = @store.exports.products.create(owner: 0)
        ExportProductsWorker.perform_async(@products.map(&:id), export.id, @store.id)
        flash[:success] = 'Espera unos minutos, el link para descargar el export aparecera en el listado de exports'
      else
        flash[:error] = 'La búsqueda actual no contiene productos, por favor intente de nuevo'
      end
      redirect_to products_path(store_id: @store.id)
    end

    def show
      @product = Mkp::Product.find(params[:id])
    end

    private

    def set_params
      @query = params[:query]
      @not_visible = params[:not_visible]
      @approved = params[:approved]
    end

    def set_store
      @store = if params[:store_id].present?
                 Mkp::Store.find params[:store_id]
               else
                 current_user.role.store
               end
    end

    def set_products
      begin
        store_ids = params[:store_id].present? ? params[:store_id] : @stores.map(&:id)
        query_ids = []
        if params[:query].present?
          title_ids = Mkp::Product.where("title LIKE '%#{params[:query]}%'").map(&:id)
          variants = Mkp::Variant.where("sku LIKE '%#{params[:query]}%'").map(&:product_id)

          query_ids = (title_ids + variants).uniq
        end

        # Verificar si hay productos antes de hacer la búsqueda
        if Mkp::Product.count == 0
          @products = []
          return
        end

        search = Mkp::Product.search do
          with :store_id, store_ids
          with :available, true
          with :visible_shop, params[:not_visible].blank?
          if query_ids.present?
            with :id, query_ids
          end

          if params[:shop_id].present?
            with :shop_id, params[:shop_id]
          end

          if params[:approved].present?
            with :product_status, params[:approved]
          end

          # if params[:query].present?
          #   all do
          #     fulltext params[:query].downcase.gsub(/[^a-z0-9\- ]/, '') do
          #       fields(:title)
          #       fields(:variants_skus)
          #     end
          #   end
          # end
          #
          # fulltext params[:query] if params[:query].present?

          order_by(:score, :desc)
          order_by(:created_at, :desc)
          paginate page: (params[:page] || 1), per_page: 30
        end
        @products = search.results
      rescue RSolr::Error::Http, RSolr::Error::ConnectionRefused => e
        # Si hay un error de Solr, simplemente mostrar una lista vacía
        Rails.logger.error("Error de Solr en products#index: #{e.message}")
        @products = []
      end
    end

    def set_products_for_export
      begin
        store_ids = params[:store_id].present? ? params[:store_id] : @stores.map(&:id)
        max_count = Mkp::Product.count

        # Si no hay productos, devolver una lista vacía
        if max_count == 0
          @products = []
          return
        end

        search = Mkp::Product.search do
          with :store_id, store_ids
          if params[:not_visible].present?
            with :visible_shop, false
          else
            with :visible_shop, true
          end
          if params[:shop_id].present?
            with :shop_id, params[:shop_id]
          end
          if params[:query].present?
            fulltext params[:query] do
              phrase_slop 1
            end
          end
          all_of do
          if params[:approved].present?
            case params[:approved]
            when 'approved'
              with :approved_for_store_ids, params[:store_id]
            when 'rejected'
              with :rejected_for_store_ids, params[:store_id]
            when 'pending'
              without :approved_for_store_ids, params[:store_id]
              without :rejected_for_store_ids, params[:store_id]
            end
          end
          end
          order_by(:created_at, :desc)
          paginate page: 1, per_page: max_count
        end
        @products = search.results
      rescue RSolr::Error::Http, RSolr::Error::ConnectionRefused => e
        # Si hay un error de Solr, simplemente mostrar una lista vacía
        Rails.logger.error("Error de Solr en products#export: #{e.message}")
        @products = []
      end
    end

    def check_visa_puntos
      @store = Mkp::Store.find_by_id(params[:store_id])
      @show_visa_puntos = @store.present? && @store.respond_to?(:has_visa_puntos?) && @store.has_visa_puntos?
    end

    def have_permissions_to_read
      redirect_to home_url, alert: "Have not permissions" if cannot?(:read, "Product")
    end

    def have_permissions_to_write
      redirect_to products_path, alert: "Have not permissions" if cannot?(:crud, "Product")
    end
  end
end
