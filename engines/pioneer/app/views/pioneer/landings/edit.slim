= content_for :title do
  | Pioneer - Pages

= content_for :js_main_lib do
  = javascript_include_tag 'pioneer/v2/application'

= content_for :js do
  script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"
  script src="https://cdn.ckeditor.com/4.7.1/full/ckeditor.js"
  javascript:
    $(function() {
      function updatePositions() {
        var data = [];
        var url = '#{update_positions_landing_path(@landing)}';

        $.each($(".components-container .sortably"), function (index, element) {
          data.push({id: $(element).attr('data-id'), position: index});
        });


        $.ajax({
          url: url,
          type: 'POST',
          data: {positions: data}
        });
      }

      $('.components-container').sortable({
        update: function (event, ui) {
          updatePositions();
        }
      });
    });

.row
  = render partial: 'form'

.components-container
  - if @components.present?
    - @components.each do |component|
      div.sortably (data-id="#{component.id}")
        = render partial: "pioneer/landings/components/#{component_partial_name(component)}", locals: { component: component, url:ajax_component_path(component), id: "component-#{component.object_id}" }
  - else
    .alert.alert-info
      | No hay componentes para mostrar. Agrega un nuevo componente usando el formulario de abajo.
.row.new-component
  .panel.panel-default
    .panel-body
        - new_component = @landing.components.build
        = form_for new_component, as: :component, url: new_ajax_component_path, method: 'get', remote: true do |f|
          .row
            .col-xs-2
              h4 New Component
            .col-xs-9
              = f.hidden_field :landing_id, value: @landing.id
              - options = ::Pages::Components::Base::AVAILABLE_COMPONENTS.collect { |c| [c[:name], c[:type]] }
              = f.select :type, options_for_select(options),{label: false, include_blank: "Elija un nuevo componente"},{class: "form-control"}
            .col-xs-1
              = f.submit 'Add',class: 'btn btn-primary'
