- url ||= ajax_components_path
- id ||= nil
.component.cols4-variants-v1 id=(id if id.present?) data-component-name='cols4_variants_v1'
  = form_for component, as: :component, url: url do |component_builder|
    = component_builder.hidden_field :landing_id, value: component.landing.id
    = component_builder.hidden_field :type, value: component.type
    .panel.panel-default
      .panel-heading
        .row
          .col-xs-6
            h4 Products Component
          .col-xs-6.text-right.actions
            = component_builder.submit 'Save',class: 'btn btn-primary mr-2'
            - remove_url = component.persisted? ? ajax_component_path(component) : nil
            a.remove.btn.btn-secondary data-action=remove_url
              | Remove
        = render partial: 'pioneer/landings/components/shared/activation_section', locals: { component: component, component_builder: component_builder }
      .panel-body
        .row
          .col-xs-12
            = text_field_tag "component[title]", component[:title], placeholder: "Title",class:"form-control my-3"
            = text_field_tag "component[setup][view_more_url]", component.setup[:view_more_url], placeholder: "View more url",class:"form-control my-3"
        .row
          .col-xs-12
            .row
              - items = component.setup.present? ? (component.setup[:items] || []) : []
              - items.each_with_index do |item, index|
                - variant = item[:variant_gp_sku].present? ? Mkp::Variant.find_by_gp_sku(item[:variant_gp_sku]) : nil
                - if variant.nil?
                  - variant = item[:variant_id].present? ? Mkp::Variant.find_by_id(item[:variant_id]) : nil
                - image = variant && variant.get_thumb || nil
                .setup.col-xs-12.col-md-3
                  = select_tag("featured_product_#{index + 1}_id",nil,
                          'data-variants_url': ajax_variants_url,
                          'data-product_title': variant&.title,
                          'data-product_id': variant&.id,
                          'data-variant_gp_sku': variant&.gp_sku,
                          'data-landing_id': component.landing&.id,
                          class:'form-control featured-product-input my-4',
                          id: "featured_product_#{index + 1}_id")

                  .chose
                    .variant-image
                      img.thumbnail.w-100 src=image
                    - if variant && variant.quantity.zero?
                      div.text-danger
                        | <i class="fa fa-exclamation-triangle"></i> Sin stock
                    - if variant && !variant.visible?
                      div.text-danger
                        | <i class="fa fa-exclamation-triangle"></i> No visible
                  = hidden_field_tag "component[setup][items][][variant_gp_sku]", item[:variant_gp_sku] || variant&.gp_sku, class: 'variant-sku'
