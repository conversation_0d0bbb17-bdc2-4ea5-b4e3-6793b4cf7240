= content_for :title do
  Pioneer - Productos

.row
  .small-12.columns
    = render partial: 'filters'

.row
  %hr
  .small-12.large-6.columns
    - if @store.present?
      %h4 #{@store.title} #{t('pioneer.products.products')}
    - else
      %h4=t('pioneer.products.products')
.row
  .small-12.columns.text-right
    - if @store.present?
      - if can?(:crud, "Product") && @store.product_approval
        %a{ href: "#", title: t('pioneer.products.approve-selected'), class: 'btn btn-primary success approve-selected-button' }
          = t('pioneer.products.approve-selected')
        %a{ href: "#", title: t('pioneer.products.reject-selected'), class: 'btn btn-primary success reject-selected-button' }
          = t('pioneer.products.reject-selected')
    - if can?(:crud, "Product")
      = link_to t('pioneer.products.list-export'), exports_path(:export_type => 'products'), class: 'btn btn-primary success'
      = link_to t('pioneer.products.export-all'), export_products_path(params), class: 'btn btn-primary info'
- if @products.present?
  .row
    .col-md-8.columns
      = page_entries_info(@products)
  .row
    .pagination.right
      = will_paginate(@products)
      %br
      %br

.row
  .small-12.columns
    - if @products.present?
      %table.table{ width: '100%' }
        %thead
          - if @store.present?
            %th= check_box_tag "check-all"
          %th{:style => "min-width: 200px;"}= t('pioneer.products.title')
          %th= t('pioneer.products.sku')
          %th= t('pioneer.products.shop')
          %th= t('pioneer.products.category')
          %th= t('pioneer.products.visible')
          %th= t('pioneer.products.price')
          %th= t('pioneer.products.sale-price')
          %th.date= t('pioneer.products.created-at')
          %th.date= t('pioneer.products.sale-from')
          %th.date= t('pioneer.products.sale-to')
          %th= t('pioneer.products.stock')
          %th= t('pioneer.products.sold')
          - if @store.present?
            - if @store.respond_to?(:product_approval) && @store.product_approval
              %th= t('pioneer.products.status')
            - if @show_visa_puntos
              %th= t('pioneer.products.visa-puntos')
            - if @show_visa_puntos || (@store.respond_to?(:product_approval) && @store.product_approval)
              %th= t('pioneer.products.actions')
        %tbody
          - @products.each do |product|
            %tr
              - if @store.present?
                %td= check_box_tag "check-#{product.id}", '1', false, { class: 'check-product', data: { 'product-id' => product.id, 'store-id' => @store.id } }
              %td{:style => "min-width: 200px;"}
                %b= link_to product.title, product.get_url + "?store_id=#{@store.try(:id)}", target: '_blank'
              %td
                - if product.respond_to?(:variants)
                  - product.variants.each do |v|
                    -if v.sku.present? && v.sku != ''
                      = v.sku
                      %br
              %td= product.shop.title if product.respond_to?(:shop) && product.shop
              %td= product.categories.first.name if product.respond_to?(:categories) && product.categories.first
              %td= product.visible? if product.respond_to?(:visible?)
              %td
                %b= number_to_currency(product.regular_price, precision: 2) if product.respond_to?(:regular_price)
              %td= number_to_currency(product.sale_price, precision: 2) if product.respond_to?(:sale_price) && product.sale_price && product.sale_price > 0
              %td= product.created_at.strftime('%Y-%m-%d') if product.respond_to?(:created_at) && product.created_at
              %td= product.sale_on.strftime('%Y-%m-%d') if product.respond_to?(:sale_on) && product.sale_on
              %td= product.sale_until.strftime('%Y-%m-%d') if product.respond_to?(:sale_until) && product.sale_until
              %td= product.total_stock if product.respond_to?(:total_stock)
              %td= product.sold_count if product.respond_to?(:sold_count)
              - if @store.present?
                - if @show_visa_puntos || (@store.respond_to?(:product_approval) && @store.product_approval)
                  - product_store = @store.product_stores.find_or_initialize_by(product_id: product.id) rescue nil
                  = form_tag(products_stores_path, remote: true, method: 'put') do |f|
                    = hidden_field_tag 'product_id', product.id
                    = hidden_field_tag 'store_id', @store.id
                    - if @store.respond_to?(:product_approval) && @store.product_approval
                      %td
                        - if product_store
                          - case product_store.status
                          - when 'approved'
                            %span.text-success.fa.fa-check{:title => t('pioneer.products.approved')}
                          - when 'rejected'
                            %span.text-danger.fa.fa-ban{:title => t('pioneer.products.rejected')}
                          - else
                            %span.text-secondary.fa.fa-question{:title => t('pioneer.products.pending')}
                    - if @show_visa_puntos
                      %td
                        = number_field_tag "visa_puntos_equivalence", product_store.try(:visa_puntos_equivalence), min: 0, max: 10, step: :any
                    %td
                      = button_tag 'Save', type: 'submit', class: "btn btn-sm btn-info"
    - else
      .alert.alert-info
        %p.text-center
          %i.fa.fa-info-circle
          No hay productos disponibles en este momento.
.row
  .small-12.columns.text-right
    - if @store.present?
      - if can?(:crud, "Product") && @store.respond_to?(:product_approval) && @store.product_approval
        %a{ href: "#", title: t('pioneer.products.approve-selected'), class: 'btn btn-primary success approve-selected-button' }
          = t('pioneer.products.approve-selected')
        %a{ href: "#", title: t('pioneer.products.reject-selected'), class: 'btn btn-primary success reject-selected-button' }
          = t('pioneer.products.reject-selected')
    - if can?(:crud, "Product") && @products.present?
      = link_to t('pioneer.products.list-export'), exports_path, class: 'btn btn-primary success'
      = link_to t('pioneer.products.export-all'), export_products_path(params), class: 'btn btn-primary info'
- if @products.present?
  .row
    .small-12.pagination.right
      = will_paginate(@products)
