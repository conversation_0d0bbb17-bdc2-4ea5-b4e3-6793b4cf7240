GIT
  remote: *******************:backend/platform/avenida-wrappers.git
  revision: ea54fe7d09f8d35c915852a27c6a4d0aea38620b
  branch: main
  specs:
    avenida_wrappers (0.1.0)

GIT
  remote: **************:avenida-maas/avenida-krabpack.git
  revision: 2778c6c0508a3a1e55987b456deabfb56226693b
  branch: main
  specs:
    avenida-krabpack (0.1.0)

GIT
  remote: **************:avenida-maas/avenida-payments.git
  revision: 03b843caebe651179c1ffebb5a8e52e531e6e9d0
  branch: columbia-main
  specs:
    avenida-payments (0.1.0)

GIT
  remote: **************:benoist/mail_view.git
  revision: fedce72c571e7a3afab015a7a6b6b997483833fb
  specs:
    mail_view (2.0.4)
      tilt

GIT
  remote: **************:goodpeople/listable.git
  revision: 89deda460ec106b63592a2b6d58c03aa69a9ccee
  specs:
    listable (0.2.0)
      activerecord (>= 3.2)
      activesupport (>= 3.2)
      composite_primary_keys (>= 5.0)

GIT
  remote: **************:goodpeople/mercadolibre.git
  revision: b42596175088d887ab2d03834eb2b6dde865e320
  specs:
    mercadolibre (0.8.7)
      rest-client (>= 1.6.7)

GIT
  remote: **************:goodpeople/oca-epak.git
  revision: e1d3c2e812ae74c155ca8a960ccd106f26ede592
  branch: add_ingreso_or_multiples_retiros
  specs:
    oca-epak (1.6.0)
      savon (~> 2.11)

GIT
  remote: **************:goodpeople/omniauth-mercadolibre-ar.git
  revision: d8735967d92597b66624aeb148ddd3fd2193d2dd
  branch: rails-5
  specs:
    omniauth-mercadolibre (0.0.4)
      omniauth
      omniauth-oauth2

GIT
  remote: **************:mjlescano/browser.git
  revision: 0afa37373ef52faec5f55c49538648e7a54b23af
  specs:
    browser (0.8.0)

PATH
  remote: engines/faily
  specs:
    faily (0.0.1)
      bootstrap-sass
      chart-js-rails
      rails (~> 4.2.10)
      sass-rails

PATH
  remote: engines/galileo
  specs:
    galileo (0.0.1)
      authlogic (>= 3.3.0)
      foundation-rails (>= 5.0.3)
      haml (>= 4.0.0)
      rails (~> 4.2.0)

PATH
  remote: engines/lux
  specs:
    lux (0.0.1)
      rails (~> 4.2.0)

PATH
  remote: engines/pioneer
  specs:
    pioneer (0.0.1)
      authlogic (>= 3.3.0)
      bourbon
      haml (>= 4.0.0)
      mail_view
      neat (~> 1.8.0)
      rails (~> 4.2.0)
      slim

GEM
  remote: https://rubygems.org/
  specs:
    actionmailer (********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 1.0, >= 1.0.5)
    actionpack (********)
      actionview (= ********)
      activesupport (= ********)
      rack (~> 1.6)
      rack-test (~> 0.6.2)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (********)
      activesupport (= ********)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    activejob (********)
      activesupport (= ********)
      globalid (>= 0.3.0)
    activemodel (********)
      activesupport (= ********)
      builder (~> 3.1)
    activerecord (********)
      activemodel (= ********)
      activesupport (= ********)
      arel (~> 6.0)
    activerecord-import (1.1.0)
      activerecord (>= 3.2)
    activeresource (4.1.0)
      activemodel (~> 4.0)
      activesupport (~> 4.0)
      rails-observers (~> 0.1.2)
    activesupport (********)
      i18n (~> 0.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    acts_as_list (1.0.4)
      activerecord (>= 4.2)
    addressable (2.8.0)
      public_suffix (>= 2.0.2, < 5.0)
    aes_key_wrap (1.1.0)
    airbrussh (1.4.0)
      sshkit (>= 1.6.1, != 1.7.0)
    ajax-datatables-rails (0.4.3)
      railties (>= 4.0)
    akami (1.3.1)
      gyoku (>= 0.4.0)
      nokogiri
    alchemist (0.1.8)
    ancestry (3.2.1)
      activerecord (>= 4.2.0)
    arel (6.0.4)
    ast (2.4.2)
    async_request (1.0.0)
      jwt (~> 2.1)
      rails (>= 4.2)
      sidekiq (>= 4.0, < 6)
    attr_required (1.0.1)
    authlogic (4.5.0)
      activerecord (>= 4.2, < 5.3)
      activesupport (>= 4.2, < 5.3)
      request_store (~> 1.0)
      scrypt (>= 1.2, < 4.0)
    auto_html (1.6.4)
      redcarpet (~> 3.1)
      rinku (~> 1.5.0)
    autoprefixer-rails (********)
      execjs (~> 2)
    aws-eventstream (1.2.0)
    aws-partitions (1.543.0)
    aws-sdk (1.61.0)
      aws-sdk-v1 (= 1.61.0)
    aws-sdk-core (3.125.0)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.525.0)
      aws-sigv4 (~> 1.1)
      jmespath (~> 1.0)
    aws-sdk-kms (1.53.0)
      aws-sdk-core (~> 3, >= 3.125.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.96.2)
      aws-sdk-core (~> 3, >= 3.112.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.1)
    aws-sdk-v1 (1.61.0)
      json (~> 1.4)
      nokogiri (>= 1.4.4)
    aws-sigv4 (1.4.0)
      aws-eventstream (~> 1, >= 1.0.2)
    babel-source (5.8.35)
    babel-transpiler (0.7.0)
      babel-source (>= 4.0, < 6)
      execjs (~> 2.0)
    barby (0.6.8)
    bcrypt (3.1.16)
    benchmark-ips (2.9.2)
    better_errors (2.9.1)
      coderay (>= 1.0.0)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
    bindata (2.4.10)
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    bootstrap-sass (3.4.1)
      autoprefixer-rails (>= 5.2.1)
      sassc (>= 2.0.0)
    bourbon (4.0.2)
      sass (~> 3.3)
      thor
    braintree (4.2.0)
      builder (>= 3.2.4)
      rexml (>= 3.1.9)
    builder (3.2.4)
    byebug (11.1.3)
    cancancan (3.3.0)
    capistrano (3.16.0)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.0.1)
      capistrano (~> 3.1)
    capistrano-rails (1.6.1)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rvm (0.1.2)
      capistrano (~> 3.0)
      sshkit (~> 1.2)
    capistrano3-puma (5.0.4)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (>= 4.0, < 6.0)
    capybara (3.36.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-slow_finder_errors (0.1.5)
      capybara (~> 3.0)
    chart-js-rails (0.1.7)
      railties (> 3.1)
    childprocess (4.1.0)
    chronic (0.10.2)
    chunky_png (1.4.0)
    climate_control (0.2.0)
    closure-compiler (1.1.14)
    cocoon (1.2.15)
    coderay (1.1.3)
    coffee-rails (4.1.1)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0, < 5.1.x)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    colorize (0.8.1)
    colppy (0.3.3)
      faraday (>= 0.8, < 0.10)
      multi_json (~> 1.3)
    commands (0.2.1)
      rails (>= 3.2.0)
    composite_primary_keys (8.1.8)
      activerecord (~> 4.2.0)
    concurrent-ruby (1.1.9)
    connection_pool (2.2.5)
    crack (0.4.5)
      rexml
    crass (1.0.6)
    credit_card_bins (0.0.3)
    css_parser (1.11.0)
      addressable
    daemons (1.4.1)
    database_cleaner (1.99.0)
    debug_inspector (1.1.0)
    devise (4.8.1)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.0)
    docile (1.4.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    draper (1.4.0)
      actionpack (>= 3.0)
      activemodel (>= 3.0)
      activesupport (>= 3.0)
      request_store (~> 1.0)
    easy_translate (0.5.1)
      thread
      thread_safe
    easypost (3.3.0)
    erubi (1.10.0)
    erubis (2.7.0)
    ethon (0.15.0)
      ffi (>= 1.15.0)
    eventmachine (1.2.7)
    exception_notification (4.2.2)
      actionmailer (>= 4.0, < 6)
      activesupport (>= 4.0, < 6)
    execjs (2.8.1)
    exifr (1.3.9)
    ey_config (0.0.7)
    factory_bot (5.2.0)
      activesupport (>= 4.2.0)
    factory_bot_rails (5.2.0)
      factory_bot (~> 5.2.0)
      railties (>= 4.2.0)
    faker (2.2.1)
      i18n (>= 0.8)
    faraday (0.9.2)
      multipart-post (>= 1.2, < 3)
    fast_blank (1.0.1)
    fasterer (0.9.0)
      colorize (~> 0.7)
      ruby_parser (>= 3.14.1)
    fb_graph (2.7.17)
      httpclient (>= 2.4)
      multi_json (>= 1.3)
      rack-oauth2 (>= 0.14.4)
      tzinfo
    ffaker (2.18.0)
    ffi (1.15.4)
    ffi-compiler (1.0.1)
      ffi (>= 1.0.0)
      rake
    filterrific (4.0.1)
    flamegraph (0.9.5)
    font-awesome-rails (*******)
      railties (>= 3.2, < 8.0)
    fontcustom (2.0.0)
      json (~> 1.4)
      listen (>= 1.0, < 4.0)
      thor (~> 0.14)
    foundation-rails (*******)
      railties (>= 3.1.0)
      sass (>= 3.3.0)
      sprockets-es6 (>= 0.9.0)
    foundation_rails_helper (4.0.0)
      actionpack (>= 4.1, < 7.0)
      activemodel (>= 4.1, < 7.0)
      activesupport (>= 4.1, < 7.0)
      railties (>= 4.1, < 7.0)
    friendly_id (5.4.2)
      activerecord (>= 4.0.0)
    fspath (3.1.2)
    geocoder (1.6.7)
    geoip (1.6.4)
    gibbon (3.2.0)
      faraday (>= 0.9.1)
      multi_json (>= 1.11.0)
    globalid (0.4.2)
      activesupport (>= 4.2.0)
    graphql (1.13.2)
    graphql-client (0.17.0)
      activesupport (>= 3.0)
      graphql (~> 1.10)
    gyoku (1.3.1)
      builder (>= 2.1.2)
    haml (5.2.2)
      temple (>= 0.8.0)
      tilt
    hashdiff (1.0.1)
    hashie (5.0.0)
    highline (2.0.3)
    hipchat (1.6.0)
      httparty
      mimemagic
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    http_logger (0.5.1)
    httparty (0.20.0)
      mime-types (~> 3.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    httpi (2.5.0)
      rack
      socksify
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    i18n-js (3.0.11)
      i18n (>= 0.6.6, < 2)
    i18n-tasks (0.7.13)
      activesupport
      easy_translate (>= 0.5.0)
      erubis
      highline
      i18n
      slop (~> 3.5)
      term-ansicolor
      terminal-table
    image_optim (0.31.1)
      exifr (~> 1.2, >= 1.2.2)
      fspath (~> 3.0)
      image_size (>= 1.5, < 4)
      in_threads (~> 1.3)
      progress (~> 3.0, >= 3.0.1)
    image_optim_pack (0.7.0.20211002)
      fspath (>= 2.1, < 4)
      image_optim (~> 0.19)
    image_size (3.0.1)
    in_threads (1.5.4)
    issuu (0.3.0)
      activesupport (>= 3.0.3)
      i18n
      multipart-post
    jbuilder (2.9.1)
      activesupport (>= 4.2.0)
    jmespath (1.4.0)
    jquery-datatables-rails (3.4.0)
      actionpack (>= 3.1)
      jquery-rails
      railties (>= 3.1)
      sass-rails
    jquery-rails (4.4.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (1.8.6)
    json-jwt (1.13.0)
      activesupport (>= 4.2)
      aes_key_wrap
      bindata
    json-schema (2.8.1)
      addressable (>= 2.4)
    jwt (2.2.3)
    kgio (2.11.4)
    launchy (2.5.0)
      addressable (~> 2.7)
    legato (0.7.0)
      multi_json
    letter_opener (1.7.0)
      launchy (~> 2.2)
    listen (3.1.5)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
      ruby_dep (~> 1.2)
    loofah (2.13.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    matrix (0.4.2)
    mechanize (2.8.3)
      addressable (~> 2.8)
      domain_name (~> 0.5, >= 0.5.20190701)
      http-cookie (~> 1.0, >= 1.0.3)
      mime-types (~> 3.0)
      net-http-digest_auth (~> 1.4, >= 1.4.1)
      net-http-persistent (>= 2.5.2, < 5.0.dev)
      nokogiri (~> 1.11, >= 1.11.2)
      rubyntlm (~> 0.6, >= 0.6.3)
      webrick (~> 1.7)
      webrobots (~> 0.1.2)
    memory_profiler (1.0.0)
    mercadopago (3.0.0)
      faraday (~> 0.9.0)
      json (~> 1.4)
    meta_request (0.7.3)
      rack-contrib (>= 1.1, < 3)
      railties (>= 3.0.0, < 7)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_mime (1.1.2)
    mini_portile2 (2.6.1)
    minitest (5.15.0)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.1.1)
    mysql2 (0.5.3)
    neat (1.8.0)
      sass (>= 3.3)
      thor (~> 0.19)
    net-http-digest_auth (1.4.1)
    net-http-persistent (4.0.1)
      connection_pool (~> 2.2)
    net-scp (3.0.0)
      net-ssh (>= 2.6.5, < 7.0.0)
    net-sftp (3.0.0)
      net-ssh (>= 5.0.0, < 7.0.0)
    net-ssh (6.1.0)
    netrc (0.11.0)
    newrelic_rpm (7.1.0)
    nio4r (2.5.8)
    nokogiri (1.12.5)
      mini_portile2 (~> 2.6.1)
      racc (~> 1.4)
    nori (2.6.0)
    oauth2 (1.4.7)
      faraday (>= 0.8, < 2.0)
      jwt (>= 1.0, < 3.0)
      multi_json (~> 1.3)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 3)
    omniauth (2.0.4)
      hashie (>= 3.4.6)
      rack (>= 1.6.2, < 3)
      rack-protection
    omniauth-identity (3.0.9)
      bcrypt
      omniauth
    omniauth-oauth2 (1.7.2)
      oauth2 (~> 1.4)
      omniauth (>= 1.9, < 3)
    omniauth-shopify-oauth2 (2.3.2)
      activesupport
      omniauth-oauth2 (~> 1.5)
    open_uri_redirections (0.2.1)
    options (2.3.2)
    orm_adapter (0.5.0)
    paleta (0.2.3)
    paper_trail (10.3.1)
      activerecord (>= 4.2)
      request_store (~> 1.1)
    paperclip (6.1.0)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      mime-types
      mimemagic (~> 0.3.0)
      terrapin (~> 0.6.0)
    paperclip-optimizer (2.0.0)
      image_optim (~> 0.19)
      paperclip (>= 3.4)
    parallel (1.21.0)
    parser (*******)
      ast (~> 2.4.1)
    power_assert (2.0.1)
    pr_geohash (1.0.0)
    progress (3.6.0)
    progress_bar (1.3.3)
      highline (>= 1.6, < 3)
      options (~> 2.3.0)
    public_suffix (4.0.6)
    puma (5.3.2)
      nio4r (~> 2.0)
    quiet_assets (1.1.0)
      railties (>= 3.1, < 5.0)
    rabl (0.14.5)
      activesupport (>= 2.3.14)
    racc (1.6.0)
    rack (1.6.13)
    rack-attack (6.5.0)
      rack (>= 1.0, < 3)
    rack-contrib (1.8.0)
      rack (~> 1.4)
    rack-cors (1.0.6)
      rack (>= 1.6.0)
    rack-mini-profiler (2.3.3)
      rack (>= 1.2.0)
    rack-oauth2 (1.12.0)
      activesupport
      attr_required
      httpclient
      json-jwt (>= 1.11.0)
      rack (< 2.1)
    rack-protection (1.5.5)
      rack
    rack-proxy (0.7.0)
      rack
    rack-reverse-proxy (0.12.0)
      rack (>= 1.0.0)
      rack-proxy (~> 0.6, >= 0.6.1)
    rack-test (0.6.3)
      rack (>= 1.0)
    rails (********)
      actionmailer (= ********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      activemodel (= ********)
      activerecord (= ********)
      activesupport (= ********)
      bundler (>= 1.3.0, < 2.0)
      railties (= ********)
      sprockets-rails
    rails-deprecated_sanitizer (1.0.4)
      activesupport (>= 4.2.0.alpha)
    rails-dom-testing (1.0.9)
      activesupport (>= 4.2.0, < 5.0)
      nokogiri (~> 1.6)
      rails-deprecated_sanitizer (>= 1.0.1)
    rails-html-sanitizer (1.4.2)
      loofah (~> 2.3)
    rails-observers (0.1.5)
      activemodel (>= 4.0)
    rails_routes_drawer (0.1.1)
    railties (********)
      actionpack (= ********)
      activesupport (= ********)
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rainbow (3.0.0)
    raindrops (0.20.0)
    rake (13.0.6)
    rb-fsevent (0.11.0)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rdoc (4.3.0)
    redcarpet (3.5.1)
    redis (3.3.5)
    redis-actionpack (5.1.0)
      actionpack (>= 4.0, < 7)
      redis-rack (>= 1, < 3)
      redis-store (>= 1.1.0, < 2)
    redis-activesupport (5.2.1)
      activesupport (>= 3, < 7)
      redis-store (>= 1.3, < 2)
    redis-namespace (1.8.1)
      redis (>= 3.0.4)
    redis-rack (2.0.6)
      rack (>= 1.5, < 3)
      redis-store (>= 1.2, < 2)
    redis-rails (5.0.2)
      redis-actionpack (>= 5.0, < 6)
      redis-activesupport (>= 5.0, < 6)
      redis-store (>= 1.2, < 2)
    redis-store (1.6.0)
      redis (>= 2.2, < 5)
    regexp_parser (2.2.0)
    request_store (1.5.0)
      rack (>= 1.4)
    responders (2.4.1)
      actionpack (>= 4.2.0, < 6.0)
      railties (>= 4.2.0, < 6.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.2.5)
    rinku (1.5.1)
    rmagick (2.13.4)
    roadie (4.0.0)
      css_parser (~> 1.4)
      nokogiri (~> 1.8)
    role_model (0.8.2)
    rollbar (3.3.0)
    roo (2.8.3)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rqrcode (2.0.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    rsolr (2.4.0)
      builder (>= 2.1.2)
      faraday (>= 0.9.0)
    rspec-core (3.9.3)
      rspec-support (~> 3.9.3)
    rspec-expectations (3.9.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-mocks (3.9.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-rails (3.9.1)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      railties (>= 3.0)
      rspec-core (~> 3.9.0)
      rspec-expectations (~> 3.9.0)
      rspec-mocks (~> 3.9.0)
      rspec-support (~> 3.9.0)
    rspec-support (3.9.4)
    rswag (1.5.2)
      rswag-api (= 1.5.2)
      rswag-specs (= 1.5.2)
      rswag-ui (= 1.5.2)
    rswag-api (1.5.2)
      railties (>= 3.1, < 6.0)
    rswag-specs (1.5.2)
      activesupport (>= 3.1, < 6.0)
      json-schema (~> 2.2)
      railties (>= 3.1, < 6.0)
    rswag-ui (1.5.2)
      actionpack (>= 3.1, < 6.0)
      railties (>= 3.1, < 6.0)
    rubocop (1.17.0)
      parallel (~> 1.10)
      parser (>= 3.0.0.0)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml
      rubocop-ast (>= 1.7.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.15.1)
      parser (>= *******)
    rubocop-performance (1.11.5)
      rubocop (>= 1.7.0, < 2.0)
      rubocop-ast (>= 0.4.0)
    rubocop-rails (2.11.3)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.7.0, < 2.0)
    rubocop-rspec (2.4.0)
      rubocop (~> 1.0)
      rubocop-ast (>= 1.1.0)
    ruby-progressbar (1.11.0)
    ruby_dep (1.5.0)
    ruby_http_client (3.5.2)
    ruby_parser (3.18.1)
      sexp_processor (~> 4.16)
    rubyntlm (0.6.3)
    rubyzip (2.3.2)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    savon (2.12.1)
      akami (~> 1.2)
      builder (>= 2.1.2)
      gyoku (~> 1.2)
      httpi (~> 2.3)
      nokogiri (>= 1.8.1)
      nori (~> 2.4)
      wasabi (~> 3.4)
    scenic (1.5.5)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    scenic-mysql_adapter (1.0.1)
      mysql2
      scenic (>= 1.4.0)
    scrypt (3.0.7)
      ffi-compiler (>= 1.0, < 2.0)
    scss_lint (0.59.0)
      sass (~> 3.5, >= 3.5.5)
    sdoc (0.4.2)
      json (~> 1.7, >= 1.7.7)
      rdoc (~> 4.0)
    select2-rails (4.0.2)
      thor (~> 0.14)
    sendgrid-ruby (6.4.0)
      ruby_http_client (~> 3.4)
    sexp_processor (4.16.0)
    shopify_api (9.4.1)
      activeresource (>= 4.1.0, < 6.0.0)
      graphql-client
      rack
    shoulda-matchers (4.4.1)
      activesupport (>= 4.2.0)
    sidekiq (4.2.10)
      concurrent-ruby (~> 1.0)
      connection_pool (~> 2.2, >= 2.2.0)
      rack-protection (>= 1.5.0)
      redis (~> 3.2, >= 3.2.1)
    sidekiq_status (1.2.0)
      sidekiq (>= 3.3, < 5)
    signet (0.12.0)
      addressable (~> 2.3)
      faraday (~> 0.9)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_enum (1.6.9)
      activesupport (>= 3.0.0)
    simple_oauth (0.2.0)
    simplecov (0.21.2)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.3)
    sinatra (1.4.8)
      rack (~> 1.5)
      rack-protection (~> 1.4)
      tilt (>= 1.3, < 3)
    slim (4.1.0)
      temple (>= 0.7.6, < 0.9)
      tilt (>= 2.0.6, < 2.1)
    slop (3.6.0)
    socksify (1.7.1)
    soulmate (1.1.0)
      multi_json (>= 1.0)
      redis (>= 3.0)
      sinatra (>= 1.0)
      vegas (>= 0.1.0)
    spring (2.1.1)
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-es6 (0.9.2)
      babel-source (>= 5.8.11)
      babel-transpiler
      sprockets (>= 3.0.0)
    sprockets-rails (2.3.3)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      sprockets (>= 2.8, < 4.0)
    sshkit (1.21.2)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    staccato (0.5.3)
    stackprof (0.2.17)
    sunspot (2.5.0)
      pr_geohash (~> 1.0)
      rsolr (>= 1.1.1, < 3)
    sunspot_rails (2.5.0)
      rails (>= 3)
      sunspot (= 2.5.0)
    sunspot_solr (2.5.0)
    sync (0.5.0)
    temple (0.8.2)
    term-ansicolor (1.7.1)
      tins (~> 1.0)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    terrapin (0.6.0)
      climate_control (>= 0.0.3, < 1.0)
    test-unit (3.4.9)
      power_assert
    thin (1.8.1)
      daemons (~> 1.0, >= 1.0.9)
      eventmachine (~> 1.0, >= 1.0.4)
      rack (>= 1, < 3)
    thor (0.20.3)
    thread (0.2.2)
    thread_safe (0.3.6)
    tilt (2.0.10)
    tins (1.30.0)
      sync
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    typhoeus (1.4.0)
      ethon (>= 0.9.0)
    tzinfo (1.2.9)
      thread_safe (~> 0.1)
    uglifier (4.2.0)
      execjs (>= 0.3.0, < 3)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (2.1.0)
    unicorn (6.0.0)
      kgio (~> 2.6)
      raindrops (~> 0.7)
    unscoped_associations (0.7.1)
      activerecord (>= 3.2.0, < 5.1.0)
    uuidtools (2.2.0)
    vegas (0.1.11)
      rack (>= 1.0.0)
    warden (1.2.7)
      rack (>= 1.0)
    wasabi (3.6.1)
      addressable
      httpi (~> 2.0)
      nokogiri (>= 1.4.2)
    web-console (2.3.0)
      activemodel (>= 4.0)
      binding_of_caller (>= 0.7.2)
      railties (>= 4.0)
      sprockets-rails (>= 2.0, < 4.0)
    webmock (3.13.0)
      addressable (>= 2.3.6)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.7.0)
    webrobots (0.1.2)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    where-or (0.1.6)
    wicked_pdf (2.1.0)
      activesupport
    will_paginate (3.0.12)
    wkhtmltopdf-binary (********)
    xpath (3.2.0)
      nokogiri (~> 1.8)

PLATFORMS
  ruby

DEPENDENCIES
  activerecord-import (~> 1.1.0)
  acts_as_list (~> 1.0.4)
  ajax-datatables-rails (~> 0.4.3)
  alchemist (~> 0.1.8)
  ancestry (~> 3.2.1)
  async_request
  authlogic (~> 4.5.0)
  auto_html (~> 1.6.4)
  avenida-krabpack!
  avenida-payments!
  avenida_wrappers!
  aws-sdk (~> 1.61.0)
  aws-sdk-s3 (~> 1.96.1)
  barby (~> 0.6.8)
  benchmark-ips (~> 2.9.1)
  better_errors (~> 2.9.1)
  binding_of_caller (~> 1.0.0)
  bootstrap-sass (~> 3.4.1)
  bourbon (~> 4.0.0)
  braintree (~> 4.2.0)
  browser!
  bundler (~> 2.0.0.pre.3)
  byebug (~> 11.1.3)
  cancancan (~> 3.3.0)
  capistrano (~> 3.16.0)
  capistrano-bundler (~> 2.0.1)
  capistrano-rails (~> 1.6.1)
  capistrano-rvm (~> 0.1.2)
  capistrano3-puma (~> 5.0.4)
  capybara-slow_finder_errors (~> 0.1.5)
  chart-js-rails (~> 0.1.7)
  childprocess (~> 4.1.0)
  chunky_png (~> 1.4.0)
  closure-compiler (~> 1.1.14)
  cocoon (~> 1.2.15)
  coffee-rails (~> 4.1.1)
  colppy
  commands (~> 0.2.1)
  credit_card_bins (~> 0.0.3)
  database_cleaner (~> 1.99.0)
  devise (~> 4.8.0)
  draper (~> 1.4.0)
  easypost (~> 3.3.0)
  exception_notification (~> 4.2.2)
  exifr (~> 1.3.9)
  ey_config (~> 0.0.7)
  factory_bot_rails (~> 5.2.0)
  faily!
  faker (~> 2.2.1)
  fast_blank (~> 1.0.0)
  fasterer (~> 0.9.0)
  fb_graph (~> 2.7.17)
  ffaker (~> 2.18.0)
  filterrific (~> 4.0.1)
  flamegraph (~> 0.9.5)
  font-awesome-rails (~> *******)
  fontcustom (~> 2.0.0)
  foundation_rails_helper (~> 4.0.0)
  friendly_id (~> 5.4.2)
  galileo!
  geocoder (~> 1.6.7)
  geoip (~> 1.6.4)
  gibbon (~> 3.2.0)
  haml (~> 5.2.1)
  hipchat (~> 1.6.0)
  http_logger (~> 0.5.1)
  i18n-js (~> 3.0.0.rc9)
  i18n-tasks (~> 0.7.13)
  image_optim_pack (~> 0.7.0.20210511)
  issuu (~> 0.3.0)
  jbuilder (~> 2.9.1)
  jquery-datatables-rails (~> 3.4.0)
  jquery-rails (~> 4.4.0)
  jwt (~> 2.2.3)
  launchy (~> 2.5.0)
  legato (~> 0.7.0)
  letter_opener (~> 1.7.0)
  listable!
  listen (~> 3.1.1)
  lux!
  mail_view!
  mechanize (~> 2.8.1)
  memory_profiler (~> 1.0.0)
  mercadolibre!
  mercadopago
  meta_request (~> 0.7.2)
  mysql2 (~> 0.5.3)
  net-scp (~> 3.0.0)
  net-sftp (~> 3.0.0)
  newrelic_rpm (~> 7.1.0)
  oca-epak!
  omniauth-identity (~> 3.0.9)
  omniauth-mercadolibre!
  omniauth-shopify-oauth2
  open_uri_redirections (~> 0.2.1)
  paleta (~> 0.2.1)
  paper_trail (~> 10.3.1)
  paperclip (~> 6.1.0)
  paperclip-optimizer
  pioneer!
  progress_bar (~> 1.3.3)
  puma (~> 5.3.2)
  quiet_assets (~> 1.1.0)
  rabl (~> 0.14.5)
  rack-attack (~> 6.5.0)
  rack-cors (~> 1.0.6)
  rack-mini-profiler (~> 2.3.2)
  rack-reverse-proxy (~> 0.12.0)
  rails (= ********)
  rails_routes_drawer (~> 0.1.1)
  rb-fsevent (~> 0.11.0)
  redis (~> 3.3.5)
  redis-namespace
  redis-rails (= 5.0.2)
  responders (~> 2.4.1)
  rmagick (~> 2.13.4)
  roadie (~> 4.0.0)
  role_model (~> 0.8.2)
  rollbar (~> 3.3)
  roo (~> 2.8.3)
  rqrcode (~> 2.0.0)
  rspec-rails (~> 3.9.1)
  rswag (~> 1.5.2)
  rubocop (~> 1.17.0)
  rubocop-performance (~> 1.11.3)
  rubocop-rails (~> 2.11.0)
  rubocop-rspec (~> 2.4.0)
  sass-rails (~> 6.0.0)
  savon (~> 2.12.1)
  scenic (~> 1.5.4)
  scenic-mysql_adapter (~> 1.0.1)
  scss_lint (~> 0.59.0)
  sdoc (~> 0.4.2)
  select2-rails (= 4.0.2)
  sendgrid-ruby (~> 6.4.0)
  shopify_api (~> 9.4.1)
  shoulda-matchers (~> 4.4.1)
  sidekiq (< 5)
  sidekiq_status
  signet (~> 0.12.0)
  simple_enum (~> 1.6)
  simple_oauth (~> 0.2.0)
  simplecov (~> 0.21.2)
  slim (~> 4.1.0)
  soulmate (~> 1.1.0)
  spring (~> 2.1.1)
  sprockets-rails (~> 2.3.3)
  staccato (~> 0.5.3)
  stackprof (~> 0.2.17)
  sunspot_rails (~> 2.5.0)
  sunspot_solr (~> 2.5.0)
  test-unit (~> 3.4.4)
  thin (~> 1.8.1)
  turbolinks (~> 5.2.1)
  typhoeus (~> 1.4.0)
  uglifier (~> 4.2.0)
  unicorn (~> 6.0.0)
  unscoped_associations (~> 0.7.1)
  uuidtools (~> 2.2.0)
  web-console (~> 2.3.0)
  webmock (~> 3.13.0)
  whenever (~> 1.0.0)
  where-or (~> 0.1.6)
  wicked_pdf (~> 2.1.0)
  will_paginate (~> 3.0.0)
  wkhtmltopdf-binary (~> ********)

RUBY VERSION
   ruby 2.6.7p197

BUNDLED WITH
   2.0.0.pre.3
