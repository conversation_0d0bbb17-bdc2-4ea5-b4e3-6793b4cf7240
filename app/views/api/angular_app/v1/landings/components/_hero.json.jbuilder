items = component.setup.try(:[], :items) || []
json.banners items.map { |hash| OpenStruct.new(hash) } do |banner|
  json.title banner.title
  json.description banner.description
  json.open_in (/avenida\.com\.ar/ =~ banner.link).nil? ? '_blank' : '_self'
  json.image ::Pages::Picture.find_by_id(banner.desktop_picture_id).try(:image).try(:url, :desktop)
  json.mobile_image ::Pages::Picture.find_by_id(banner.mobile_picture_id).try(:image).try(:url, :desktop)
  json.link banner.link
end
