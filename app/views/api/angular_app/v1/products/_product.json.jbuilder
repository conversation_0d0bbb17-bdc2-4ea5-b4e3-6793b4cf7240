max_installments = product.category.max_installments(@current_store, product)
max_installments_info = product.max_installments_info(@current_store, product)

json.extract! product, :id, :title, :description, :available_properties, :available_properties_names, :currency_symbol, :currency_code, :slug,
  :purchasable, :reservable, :transaction_type
json.max_installments max_installments
json.max_installments_info max_installments_info
json.available_installments product.available_installments(@current_store)
json.available_properties product.available_properties.map { |p| p.is_a?(Hash) ? p[:slug] : p }

json.category do
  json.partial! 'api/angular_app/v1/categories/category', category: product.category
end

json.data product.data_to_simple_hash.map{ |key, value| {key: key,  value: value} }
json.data_shipment product.data_to_simple_hash(:data_shipment).map{ |key, value| {key: key,  value: value} }
json.breadcrumb Mkp::Breadcrumb.new(product).adapt

json.total_stock product.total_stock_cached
json.unavailable product.unavailable?
json.pickeable product.pickeable?
json.has_no_property product.has_no_property?
json.is_voucher product.is_voucher?
json.regular_price_without_taxes product.regular_price_without_taxes && "%.2f" % product.regular_price_without_taxes
json.regular_price "%.2f" % product.regular_price
json.regular_price_without_taxes product.regular_price_without_taxes && "%.2f" % product.regular_price_without_taxes
json.regular_price "%.2f" % product.regular_price
json.points product.get_points_price(@current_store)
json.points_equivalence product.get_points_equivalence_with_iva(@current_store)

sale_calculator = Mkp::ProductSaleDetector.new(product)
json.is_on_sale sale_calculator.is_on_sale?
if sale_calculator.is_on_sale?
  json.sale_price sale_calculator.calculate
  json.percent_off sale_calculator.percent_off
  json.sale_price_without_taxes sale_calculator.calculate_without_taxes
end

json.propertiable product.available_properties do |property|
  if property.is_a? Hash
    json.key property[:slug]
    json.data product.available_values_for(property[:slug])
    json.name property[:name]
  else
    json.key property
    json.data product.available_values_for(property)
  end
end

json.pictures product.pictures do |picture|
  json.id picture.id
  json.thumb picture.url(:t)
  json.large picture.url(:l)
  json.st picture.url(:st)
end

json.manufacturer do
  json.partial! 'api/angular_app/v1/manufacturers/manufacturer', manufacturer: product.manufacturer
end

json.shop do
  json.partial! 'api/angular_app/v1/shops/shop', shop: product.shop
end

json.variants product.variants do |variant|
  json.partial! 'api/angular_app/v1/variants/variant', variant: variant
end

json.promotions Promotions::Serializer.new(product, product.price, max_installments, @current_store).build do |promotion|
  json.partial! 'api/angular_app/v1/promotions/promotion', promotion: promotion
end
