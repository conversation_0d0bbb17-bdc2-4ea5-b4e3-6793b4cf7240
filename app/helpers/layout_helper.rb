# These helper methods can be called in your template to set variables to be used in the layout
# This module should be included in all views globally,
# to do so you may need to add this line to your ApplicationController
#   helper :layout
module LayoutHelper
  def body_id
    return 'default-body-id' unless @active_template && @active_template.virtual_path
    @active_template.virtual_path.gsub(/\/|social\/|mkp\/|mobile\//, '/' => '-', 'social/' => '', 'mkp/' => '', 'mobile/' => '')
  end

  def body_class
    content_for(:body_class) if content_for?(:body_class)
  end

  def javascript(*args)
    content_for(:head) { javascript_include_tag(*args) }
  end

  def stylesheet(*args)
    content_for(:head) { stylesheet_link_tag(*args) }
  end
end
