module Mkp::ApplicationHelper

  def price_format(price)
    number_with_precision(price).gsub("#{t('number.currency.format.separator')}00",'')
  end

  def check_payment_methods(payment_methods)
    # Verificar si hay configuraciones
    setting = Setting.first

    if setting && setting[:value] == "todopago"
      payment_methods.delete(:mercadopago)
    else
      payment_methods.delete(:todo_pago)
    end

    payment_methods
  end


end
