module Mkp
  module Attachment
    class ProductPicture < Picture
      has_attached_file :photo,
        styles: ->(a) { a.instance.class.styles },
        path: "avenida/products/:attachment/:public_folder/:token_:style.:extension",
        processors: [:thumbnail, :paperclip_optimizer],
        default_style: :st,
        convert_options: {all: '-sampling-factor 4:2:0 -strip -quality 80 -colorspace sRGB'}

      has_many :variants,
        class_name: '::Mkp::Variant',
        foreign_key: :picture_id,
        dependent: :nullify

      belongs_to :variant

      has_one :external_object,
        class_name: '::Mkp::Integration::Object',
        as: :integrable,
        dependent: :destroy

      default_scope { order('view_order ASC') }

      def real_path(style = :l)
        photo.path(style)
      end

      def real_url(style = :l)
        photo.url(style)
      end

      def url(style = :l)
        # En desarrollo, intentamos usar la URL real de la imagen
        if Rails.env.development?
          url = photo.url(style)

          if url.present? && url.start_with?('/') && File.exist?(Rails.root.join('public' + url))
            return url
          end

          if url.present?
            filename = File.basename(url.split('?').first)
            base_filename = filename.split('_').last

             demo_path = "app/assets/images/demo/#{base_filename}"
            if File.exist?(Rails.root.join(demo_path))
              return "/assets/demo/#{base_filename}"
            end

            demo_path = "app/assets/images/demo/#{filename}"
            if File.exist?(Rails.root.join(demo_path))
              return "/assets/demo/#{filename}"
            end

            name_without_ext = base_filename.split('.').first
            Dir.glob(Rails.root.join("app/assets/images/demo/#{name_without_ext}.*")).each do |file|
              return "/assets/demo/#{File.basename(file)}"
            end
          end

          # Si no encontramos la imagen, devolvemos la imagen por defecto
          return 'https://tuquejasuma.com/media/cache/e1/4e/e14e65077c992b9b31a3922d55f71095.jpg'
        end

        # En producción, usamos el comportamiento original
        if processing
          style = :m unless style == :original
          url = unpublished_photo(style, :url)
          "https://#{HOSTNAME}#{url}"
        else
          photo.url(style)
        end
      end

      def destroy
        # PROBLEMA: La falta de contexto no permite decidir correctamente si se puede o no borrar una imagen.
        # Esto se debe a que si estoy tratando de borrar un producto la imagen nunca se entera de ese contexto y no permite el borrado
        # de la ultima imagen asociada a un producto.

        # actualizo el product_id para desvincular la imagen del producto y simular que se eliminó
        # Nunca destruyo las imagenes. Se destruiran con una tarea que revisara que imagenes no tienen un producto asociado.
        # TODO: cron job que elimine las imágenes sin producto cada cierto tiempo
        self.update_attribute(:product_id, nil)
        true
      end


      private

      def should_be_destroyed?
        product.blank? || (not last_picture?)
      end

      def last_picture?
        product.pictures.count <= 1
      end

    end
  end
end
