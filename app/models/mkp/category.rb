module Mkp
  class Category < ActiveRecord::Base
    has_paper_trail
    extend FriendlyId
    include Concerns::HasSlugOnRedis
    include Concerns::HasPublishablePictures
    include ::Concerns::UrlMapperCategoryCallbacks

    as_enum :pickup, {:not_applicable=> 0, :not_pickeable=> 1, :pickeable=>  2}, {:column => :pickeable_status}

    has_many :products, dependent: :nullify
    has_and_belongs_to_many :sports,   join_table: 'categories_on_sports'
    has_and_belongs_to_many :genders,  join_table: 'categories_on_genders'
    has_many :category_stores
    has_many :stores, through: :category_stores
    has_many :installments, as: :referent

    after_save :set_full_path_name

    validates :name, presence: true
    validates :weight, :height, :width, :length, numericality: true, allow_nil: true
    validates :visa_puntos_equivalence, numericality: { greater_than: 0 }, allow_nil: true

    store :translations, accessors: [
      :pe_translation,
      :cr_translation,
      :co_translation
    ]

    scope :by_network, ->(network) { where(network: network) }
    scope :active, -> { where(active: true) }
    scope :not_active, -> { where(active: false) }
    scope :categories_for_gender, lambda { |gender, network| {
        joins: 'LEFT JOIN categories_on_genders ON mkp_categories.id = categories_on_genders.category_id',
        conditions: ['categories_on_genders.gender_id IN (?) AND mkp_categories.network = ?', gender.child_ids, network],
        order: 'mkp_categories.display_order ASC',
        group: 'mkp_categories.id'
      }
    }
    scope :with_available_products, -> { joins(products: :variants).merge(Variant.with_stock) }

    has_ancestry orphan_strategy: :rootify, cache_depth: true

    friendly_id :name, use: [:scoped, :slugged], scope: [:ancestry, :network]

    searchable do
      integer :id
      text :name
      boolean :active

      string :network, stored: true

      integer :manufacturers_ids, multiple: true do
        products.active.with_stock.joins(:manufacturer).map(&:manufacturer_id)
      end

      integer :variants_ids, multiple: true do
        products.active.with_stock.joins(:variants).flat_map(&:variants).map(&:id)
      end
    end

    def flat_pickup
      return pickup unless parent.present?
      return :not_applicable if pickup == :not_applicable && parent.flat_pickup == :not_applicable
      return :not_pickeable if pickup == :not_pickeable or parent.flat_pickup == :not_pickeable
      :pickeable
    end

    def full_path_slug
      @full_path_slug ||= self.class.full_path_string(path, '-')
    end

    def full_path
      @full_path ||= self.class.full_path_string(path, ' / ')
    end

    # def full_path_name
    #  @full_path_name ||= path.map(&:name).join('/')
    # end

    def set_full_path_name
      self.update_column(:full_path_name, path.map(&:name).join('/'))
      set_full_path_name_by_children
    end

    def set_full_path_name_by_children
      self.children.each{ |ch| ch.save! }
    end

    def self.full_path_string(categories, separator)
      categories.map(&:slug).join(separator)
    end

    def self.get_full_path_slugs_from(categories)
      categories.map do |cat|
        cats = cat.path_ids.map { |_c| categories.find { |x| x.id == _c } }
        full_path_string(cats, '-')
      end
    end

    def manufacturers
      manufacturers_ids = Mkp::Variant.search(include: variant_includes) do
        with :deleted, false
        with :shop_visible, true
        with :shop_deleted, false
        with :display_variant, true
        with :network, network
        with(:quantity).greater_than 0
        with(:available_on).less_than Time.now
        with :categories_ids, id

        facet :manufacturer_id, limit: -1
      end.facet(:manufacturer_id).rows.map(&:value)

      Mkp::Manufacturer.where(id: manufacturers_ids)
    end

    def variant_includes
      [:picture, product: [:shop, :order_items, :currency, :category, :manufacturer, :pictures, :sports]]
    end

    def should_generate_new_friendly_id?
      if new_record?
        !!active
      else
        !!active && !changes.key?(:slug)
      end
    end

    def get_dimension_values(dimension)
      unit = dimension == :weight ? :mass_unit : :length_unit
      if (measure = send(dimension)).present? && (unit = send(unit)).present?
        [measure, unit]
      elsif parent.present?
        parent.get_dimension_values(dimension)
      else
        [nil, nil]
      end
    end

    def max_installments(current_store, product = nil)
      if product
        installments = product.available_installments(current_store)
        if installments && installments.any?
          # Busca las cuotas sin interés y verifica si hay más de una
          interest_free_installments = installments.select { |inst| inst[:tea] == 0.0 }
          if interest_free_installments.size >= 1
            return interest_free_installments.max_by { |inst| inst[:number] }[:number]
          else
            # Si no hay más de una cuota sin interés, encuentra la máxima cuota con interés
            max_interest_installment = installments.select { |inst| inst[:tea] > 0.0 }.max_by { |inst| inst[:number] }
            return max_interest_installment ? max_interest_installment[:number] : nil
          end
        end
      end

      # Valor por defecto de category_stores si no hay producto o no hay cuotas
      category_stores.find_by_store_id(current_store.id).try(:max_interest_free_number).to_i
    end

    def commission(current_store)
      category_stores.find_by_store_id(current_store.id).try(:commission)
    end
  end
end
