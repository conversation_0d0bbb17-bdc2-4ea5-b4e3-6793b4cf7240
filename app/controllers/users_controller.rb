class UsersController < ApplicationController
  include Followable

  before_filter :not_logged_in_required, only: [:new, :create, :signup]
  before_filter :login_required, only: [:notifications, :read_all_notifications]
  skip_before_filter :store_return_to_location, only: [:new]
  skip_before_filter :set_locale,
                     :load_new_session,
                     :new_whatsup,
                     :store_return_to_location,
                     :bootstrap_cart,
                     :detect_google_bot_logged_in, only: [:current]

  def new
    email = params[:email]
    profile = Social::Profile::User.new(params[:profile])

    @user = SocialUser.new(email: email, profile: profile)
    @profile_bio_maxlength = Social::Profile::BIO_MAX_LENGTH

    @auth_id = params[:auth_id]
    @avatar_url = params[:avatar_url] || Social::Attachment::AvatarPicture.new.url
  end

  def create
    @user = SocialUser.new(params[:social_user])
    @user.roles = [:social]
    @user.network = @network

    if chose_avatar_from_url?
      begin
        @user.profile.assign_avatar_from_url(params[:avatar_url])
      rescue => e
        Rails.logger.info("There was an error graving the avatar from the URL in the params. Error: #{e}")
      end
    end

    if @user.save
      if authenticated_by_oauth?
        Authentication.associate_user(params[:auth_id], @user.id)
      end

      UserMailer.delay.welcome_email(@user, I18n.locale)

      # newsletter is not working properly, fix this later
      # subscribe_to_newsletter

      redirect_to :social_after_signup_follow_recommendations
    else
      render '/users/new'
    end
  end

  def brand_application
    if params[:brand_application][:im_a_bot].present? || params[:brand_application][:email].blank? || !EmailUtils.valid_email?(params[:brand_application][:email])
      if request.xhr?
        render nothing: true, status: :bad_request and return
      end
      not_found and return
    end

    UserMailer.delay.brand_application_form_email(params[:brand_application], I18n.locale)
    UserMailer.delay.brand_application_form_admin_email(params[:brand_application], I18n.locale)

    if request.xhr?
      render nothing: true, status: :ok
    else
      redirect_to pages_brands_path(network: @network.downcase)
    end
  end

  def check_login_availability
    if LoginAvailableValidator.available?(params[:login])
      render json: { taken: false, valid: true }, status: :ok
    else
      render json: { taken: true, valid: false }, status: :ok
    end
  end

  def check_email_availability
    if available_email?(params[:email])
      render json: { taken: false, valid: true }, status: :ok
    else
      render json: { taken: true, valid: false }, status: :ok
    end
  end

  def current
    user = User.find(params[:id])

    if user.can_be_managed_by?(current_logged_in_user)
      set_current_actor_id(user.id)
    end

    redirect_to profiles_url(user.login)
  end

  def signup
    # if request.xhr? || params[:xhr].present?
    #   render partial: 'users/signup_modal', locals: { on_modal: true } and return
    # end
  end

  def recommendations
    @seed = params[:seed] || Time.now.to_i
    @type = params[:type]
    @type = 'Brand' unless ['Brand', 'SocialUser'].include?(@type)
    page = params[:page] || 1
    limit = params[:limit].to_i
    limit = 60 unless limit.between?(1, 60)

    @users = Social::FollowRecommender.recommend_paginated_users_to(current_user,
                                                                    @type,
                                                                    limit,
                                                                    page,
                                                                    @seed)

    if request.xhr? || params[:xhr]
      render layout: false,
             locals: { users: @users,
                       seed: @seed,
                       type: @type,
                       with_menu: params[:page].blank? }
      return
    end

    render nothing: true
  end

  def notifications
    @notifications = Notification.of(current_user).limit(10)
    @unread_count = Notification.unread_count_of(current_user)

    render json: {
      notifications: @notifications,
      unread_count: @unread_count
    }
  end

  def read_all_notifications
    Notification.of(current_user).unread.update_all(read: true)
    render json: { success: true }
  end

  private

  def available_email?(email)
    User.where(email: email).first.blank?
  end

  def chose_avatar_from_url?
    params[:social_user][:profile_attributes][:unpublished_avatar_id].blank? &&
        params[:avatar_url].present?
  end

  def authenticated_by_oauth?
    !params[:auth_id].blank?
  end

  def subscribe_to_newsletter
    options = {}.tap do |hash|
      hash[:email] = @user.email
      hash[:first_name] = @user.first_name if @user.first_name
      hash[:last_name] = @user.last_name if @user.last_name
    end

    NewsletterSubscriptionWorker.perform_async(options, 'subscribers')
  end
end
