require 'ostruct'

class LandingsController < ApplicationController
  before_filter :force_request_format_to_html,:load_landing

  def show
    # Si @landing es un objeto temporal, no redirigir
    return if @landing.new_record?

    # Asegurarse de que @landing.path no sea nil
    if @landing.path.present?
      redirect_to @landing.path
    else
      # Si @landing.path es nil, renderizar una página estática
      render inline: '<h1>Bienvenido a Avenida</h1><p>No tienes tiendas disponibles para administrar.</p>', layout: 'application'
    end
  end

  private

  def force_request_format_to_html
    request.format = :html
  end

  def load_landing
    network = params[:network] || @network
    @landing = if params[:id].present?
      ::Pages::Landing.avenidable.active_for_network(@network).where(id: params[:id]).first
    else
      ::Pages::Landing.avenidable.home_for_network(network).last
    end

    if @landing.nil?
      # Si no hay landing, crear uno temporal para evitar errores
      # y evitar un bucle infinito de redirecciones
      @landing = ::Pages::Landing.new(network: network, home: true, active: true)

      # Crear una plantilla temporal para evitar errores con @active_template
      @active_template = OpenStruct.new(virtual_path: 'landings/show')

      # Renderizar una página estática simple
      render inline: '<h1>Bienvenido a Avenida</h1><p>No tienes tiendas disponibles para administrar.</p>', layout: 'application'
    end
  end

  def purged_params
    params.reject{ |key, _value| %(network path controller action id).include?(key.to_s) }
  end
end
