{"swagger": "2.0", "info": {"title": "API V1", "version": "v1"}, "definitions": {"auth_token": {"type": "object", "properties": {"auth_token": {"type": "string"}}}, "category": {"type": "object", "properties": {"id": {"type": "integer"}, "ancestry": {"type": "string", "description": "Ids of superior categories separated by /"}, "name": {"type": "string"}, "full_path_name": {"type": "string", "description": "Names of superior categories separated by /"}}}, "image": {"type": "object", "properties": {"url": {"type": "string"}}}, "manufacturer": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}}, "shop": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}}}, "order": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string", "x-nullable": true}, "created_at": {"type": "string"}, "total": {"type": "string"}, "status": {"type": "string"}, "payment_status": {"type": "string"}, "coupon_discount": {"type": "string"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/payment"}}, "customer": {"type": "object", "properties": {"first_name": {"type": "string"}, "last_name": {"type": "string"}, "doc_number": {"type": "string"}, "address": {"type": "string"}}}, "products": {"type": "array", "items": {"$ref": "#/definitions/order_item"}}, "shipments": {"type": "array", "items": {"$ref": "#/definitions/shipment"}}}}, "order_item": {"type": "object", "properties": {"quantity": {"type": "integer"}, "title": {"type": "string"}, "sku": {"type": "string"}, "price": {"type": "string"}}}, "package": {"type": "object", "properties": {"width": {"type": "string"}, "height": {"type": "string"}, "length": {"type": "string"}, "weight": {"type": "string"}, "length_unit": {"type": "string", "x-nullable": true}, "mass_unit": {"type": "string", "x-nullable": true}}}, "payment": {"type": "object", "properties": {"status": {"type": "string"}, "total": {"type": "string"}, "method": {"type": "string"}, "installments": {"type": "string"}, "bin": {"type": "string"}}}, "product": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "description": {"type": "string"}, "category_id": {"type": "integer"}, "manufacturer_id": {"type": "integer"}, "regular_price": {"type": "string"}, "regular_price_without_taxes": {"type": "string"}, "iva": {"type": "string"}, "available_on": {"type": "string", "format": "yyyy-mm-ddThh:mm:ss.000-03:00"}, "sale_on": {"type": "string", "format": "yyyy-mm-ddThh:mm:ss.000-03:00"}, "sale_until": {"type": "string", "format": "yyyy-mm-ddThh:mm:ss.000-03:00"}, "sale_price": {"type": "string"}, "sale_price_without_taxes": {"type": "string"}, "transaction_type": {"type": "string", "description": "Available options: purchasable, reservable, other, voucher and points"}, "shop_id": {"type": "integer"}, "variants": {"type": "array", "items": {"$ref": "#/definitions/variant"}}, "package": {"type": "array", "items": {"$ref": "#/definitions/package"}}}}, "shipment": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string"}, "destination": {"type": "object", "properties": {"name": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "province": {"type": "string"}, "floor_department": {"type": "string"}, "telephone": {"type": "string"}, "zip": {"type": "string"}, "doc_number": {"type": "string"}}}}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "number"}, "per_page": {"type": "number"}, "total_pages": {"type": "number"}}}}}, "variant": {"type": "object", "properties": {"sku": {"type": "string"}, "quantity": {"type": "string"}, "ean_code": {"type": "string"}, "properties": {"type": "object", "properties": {"property_name": {"type": "string", "description": "It can be color, size, Hardness, Length, Material, Percentage, Payment_Method"}}}, "points_price": {"type": "integer"}, "discount_top": {"type": "integer"}}}}, "basePath": "/api/v1", "securityDefinitions": {"BasicAuth": {"type": "basic"}, "client": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"client": []}], "paths": {"/authenticate": {"post": {"summary": "Grants a token to use the API after validate user and password", "security": [], "tags": ["Authentication"], "produces": ["application/json"], "parameters": [{"name": "email", "in": "query", "type": "string", "required": true}, {"name": "password", "in": "query", "type": "string", "required": true}], "responses": {"200": {"description": "Ok", "schema": {"$ref": "#/definitions/auth_token"}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "properties": {"error": {"type": "object", "properties": {"user_authentication": {"type": "string"}}}}}}}}}, "/categories": {"get": {"summary": "Get a collections of categories", "tags": ["Categories"], "produces": ["application/json"], "parameters": [{"name": "page", "in": "query", "type": "integer", "required": false}, {"name": "per_page", "in": "query", "type": "integer", "required": false}], "responses": {"200": {"description": "Ok", "schema": {"type": "object", "properties": {"categories": {"type": "array", "items": {"$ref": "#/definitions/category"}}, "meta": {"type": "object", "$ref": "#/definitions/meta"}}}}}}}, "/categories/{id}": {"get": {"summary": "Get a category by ID", "tags": ["Categories"], "produces": ["application/json"], "parameters": [{"name": "id", "in": "path", "type": "string", "required": true}], "responses": {"200": {"description": "Ok", "schema": {"$ref": "#/definitions/category"}}, "404": {"description": "Not found"}}}}, "/manufacturers": {"get": {"summary": "Get a collections of manufacturers", "tags": ["Manufacturers"], "produces": ["application/json"], "parameters": [{"name": "page", "in": "query", "type": "integer", "required": false}, {"name": "per_page", "in": "query", "type": "integer", "required": false}], "responses": {"200": {"description": "Ok", "schema": {"type": "object", "properties": {"manufacturers": {"type": "array", "items": {"$ref": "#/definitions/manufacturer"}}, "meta": {"type": "object", "$ref": "#/definitions/meta"}}}}}}}, "/manufacturers/{id}": {"get": {"summary": "Get a manufacturer by ID", "tags": ["Manufacturers"], "produces": ["application/json"], "parameters": [{"name": "id", "in": "path", "type": "string", "required": true}], "responses": {"200": {"description": "Ok", "schema": {"$ref": "#/definitions/manufacturer"}}, "404": {"description": "Not found"}}}}, "/orders": {"get": {"summary": "Get a collections of orders related to the logged user", "tags": ["Orders"], "produces": ["application/json"], "parameters": [{"name": "page", "in": "query", "type": "integer", "required": false}, {"name": "per_page", "in": "query", "type": "integer", "required": false}, {"name": "date_from", "in": "query", "type": "string", "description": "Supported format: yyyy-mm-dd", "format": "yyyy-mm-dd", "required": false}, {"name": "date_to", "in": "query", "type": "string", "description": "Supported format: yyyy-mm-dd", "format": "yyyy-mm-dd", "required": false}], "responses": {"200": {"description": "Ok", "schema": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/definitions/order"}}, "meta": {"type": "object", "$ref": "#/definitions/meta"}}}}, "404": {"description": "Not found"}}}}, "/orders/{id}": {"get": {"summary": "Get an order by ID. The order must be related to logged user", "tags": ["Orders"], "produces": ["application/json"], "parameters": [{"name": "id", "in": "path", "type": "string", "required": true}], "responses": {"200": {"description": "Ok", "schema": {"$ref": "#/definitions/order"}}, "404": {"description": "Not found"}}}}, "/products": {"get": {"summary": "Get a collections of products related to the logged user", "tags": ["Products"], "produces": ["application/json"], "parameters": [{"name": "page", "in": "query", "type": "integer", "required": false}, {"name": "per_page", "in": "query", "type": "integer", "required": false}], "responses": {"200": {"description": "Ok", "schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/definitions/product"}}, "meta": {"type": "object", "$ref": "#/definitions/meta"}}}}}}, "post": {"summary": "Create a product related to the logged user", "tags": ["Products"], "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "shop_id", "in": "body", "type": "integer", "description": "This ID must be related to an existing shop. The shop must be related to logged user", "required": true}, {"name": "title", "in": "body", "type": "string", "required": true}, {"name": "description", "in": "body", "type": "string", "required": true}, {"name": "regular_price", "in": "body", "type": "number", "required": true}, {"name": "sale_price", "in": "body", "type": "number", "required": false}, {"name": "regular_price_without_taxes", "in": "body", "type": "number", "required": true}, {"name": "shop_id", "in": "body", "type": "integer", "description": "This ID must be related to an existing shop. The shop must be related to logged user", "required": true}, {"name": "regular_price_without_taxes", "in": "body", "type": "number", "required": true}, {"name": "available_on", "in": "body", "type": "string", "description": "Supported format: dd/mm/yyyy", "format": "dd/mm/yyyy", "required": true}, {"name": "category_id", "in": "body", "type": "integer", "description": "This ID must be related to an existing category", "required": true}, {"name": "manufacturer_id", "in": "body", "type": "integer", "description": "This ID must be related to an existing manufacturer", "required": true}, {"name": "regular_price", "in": "body", "type": "number", "required": true}, {"name": "iva", "in": "body", "type": "number", "required": true}, {"name": "available_on", "in": "body", "type": "string", "description": "Supported format: dd/mm/yyyy", "format": "dd/mm/yyyy", "required": true}, {"name": "sale_on", "in": "body", "type": "string", "description": "Supported format: dd/mm/yyyy", "format": "dd/mm/yyyy", "required": false}, {"name": "sale_until", "in": "body", "type": "string", "description": "Supported format: dd/mm/yyyy", "format": "dd/mm/yyyy", "required": false}, {"name": "regular_price_without_taxes", "in": "body", "type": "number", "required": true}, {"name": "sale_price", "in": "body", "type": "number", "required": false}, {"name": "regular_price_without_taxes", "in": "body", "type": "number", "required": true}, {"name": "sale_price_without_taxes", "in": "body", "type": "number", "required": false}, {"name": "transaction_type", "in": "body", "type": "string", "description": "Available options: purchasable, reservable, other, voucher and points", "required": true}, {"name": "images", "in": "body", "type": "array", "items": {"$ref": "#/definitions/image"}, "required": true}, {"name": "variants", "in": "body", "type": "array", "description": "point_price and discount_top are optionals. property_name can be color, size, Hardness, Length, Material, Percentage, Payment_Method. property_name can be null single or multiple", "items": {"$ref": "#/definitions/variant"}, "required": true}, {"name": "package", "in": "body", "description": "length_unit and mass_unit are optionals, the default values are millimeters and kilograms.", "schema": {"$ref": "#/definitions/package"}, "required": true}], "responses": {"200": {"description": "Ok", "schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/definitions/product"}}, "meta": {"type": "object", "$ref": "#/definitions/meta"}}}}}}}, "/products/{id}": {"get": {"summary": "Get a product by ID. The product must be related to logged user", "tags": ["Products"], "produces": ["application/json"], "parameters": [{"name": "id", "in": "path", "type": "string", "required": true}], "responses": {"200": {"description": "Ok", "schema": {"$ref": "#/definitions/product"}}, "404": {"description": "Not found"}}}, "put": {"summary": "Update a product by ID. The product must be exist and must be related to logged user", "tags": ["Products"], "produces": ["application/json"], "parameters": [{"name": "id", "in": "path", "type": "string", "required": false}, {"name": "title", "in": "body", "type": "string", "required": false}, {"name": "description", "in": "body", "type": "string", "required": false}, {"name": "regular_price", "in": "body", "type": "number", "required": false}, {"name": "iva", "in": "body", "type": "number", "required": false}, {"name": "available_on", "in": "body", "type": "string", "description": "Supported format: dd/mm/yyyy", "format": "dd/mm/yyyy", "required": false}, {"name": "sale_on", "in": "body", "type": "string", "description": "Supported format: dd/mm/yyyy", "format": "dd/mm/yyyy", "required": false}, {"name": "sale_until", "in": "body", "type": "string", "description": "Supported format: dd/mm/yyyy", "format": "dd/mm/yyyy", "required": false}, {"name": "sale_price", "in": "body", "type": "number", "required": false}, {"name": "images", "in": "body", "type": "array", "items": {"$ref": "#/definitions/image"}, "required": false}, {"name": "variants", "in": "body", "type": "array", "description": "point_price and discount_top are optionals. property_name can be color, size, Hardness, Length, Material, Percentage, Payment_Method. property_name can be null single or multiple. If exist a variant with the same sku it will replaced", "items": {"$ref": "#/definitions/variant"}, "required": false}], "responses": {"200": {"description": "Ok", "schema": {"$ref": "#/definitions/product"}}, "400": {"description": "An error has ocurred"}, "404": {"description": "Not found"}}}}, "/shops": {"get": {"summary": "Get a collections of shops related to the logged user", "tags": ["Shops"], "produces": ["application/json"], "parameters": [{"name": "page", "in": "query", "type": "integer", "required": false}, {"name": "per_page", "in": "query", "type": "integer", "required": false}], "responses": {"200": {"description": "Ok", "schema": {"type": "object", "properties": {"shops": {"type": "array", "items": {"$ref": "#/definitions/shop"}}, "meta": {"type": "object", "$ref": "#/definitions/meta"}}}}}}}, "/shops/{id}": {"get": {"summary": "Get a shop by ID. The shop must be related to logged user", "tags": ["Shops"], "produces": ["application/json"], "parameters": [{"name": "id", "in": "path", "type": "string", "required": true}], "responses": {"200": {"description": "Ok", "schema": {"$ref": "#/definitions/shop"}}, "404": {"description": "Not found"}}}}}}